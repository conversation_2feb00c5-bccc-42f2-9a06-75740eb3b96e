.blog-posts {
    a {
        text-decoration: none;
        font-weight: 400;
    }

    &__col {
        position: relative;
        &:after {
            content: "";
            display: block;
            position: absolute;
            bottom: 0;
            width: calc(100% - 15px);
            border-bottom: 1px solid $midlightgrey;

            @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                width: calc(100% - 30px);
            }

            @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
                width: calc(108% + 5px);
                left: calc(-4% - 5px);
            }
        }

        &--standard {
            &:after {
                display: none;
            }

            @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                .blog-posts__post-col {
                    &:nth-child(1) {
                        order: 2;
                    }

                    &:nth-child(2) {
                        order: 1;
                    }
                }

                .blog-posts__thumbnail {
                    padding-bottom: 20px;
                    text-align: center;
                }
            }
        }

        //&:first-child,
        //&:nth-child(2),
        //&:nth-child(3),
        //&:nth-child(4) {
        //    .blog-posts__post-col {
        //        flex: 0 0 50%;
        //        max-width: 50%;
        //
        //        @media only screen and (max-width: 350px) {
        //            flex: 0 0 100%;
        //            max-width: 100%;
        //        }
        //    }
        //
        //    @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
        //        .blog-posts__post-col {
        //            flex: 0 0 100%;
        //            max-width: 100%;
        //
        //            &:nth-child(1) {
        //                order: 2;
        //            }
        //
        //            &:nth-child(2) {
        //                order: 1;
        //            }
        //
        //            .blog-posts__content {
        //                text-align: left;
        //            }
        //        }
        //    }
        //
        //    @media only screen and (min-width: map-get($grid-breakpoints, md)) {
        //        .holiday-types__content {
        //            padding-top: 0;
        //        }
        //    }
        //}

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            .blog-posts__post-col {
                flex: 0 0 100%;
                max-width: 100%;

                &:nth-child(1) {
                    order: 2;
                }

                &:nth-child(2) {
                    order: 1;
                }

                .blog-posts__content {
                    text-align: left;
                }
            }
        }

        &:last-child,
        &:nth-last-child(2) {
            @media only screen and (min-width: map-get($grid-breakpoints, md)) {
                &:after {
                    display: none;
                }
            }
        }

        &:nth-child(2n + 1) {
            &:after {
                margin-right: -15px;

                @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                    margin-right: 0;
                }
            }

            .blog-posts__post-col {
                &:last-child {
                    padding-right: 50px;

                    @media only screen and (max-width: map-get($grid-breakpoints-max, xl)) {
                        padding-right: 15px;
                    }

                    @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
                        padding-right: 5px;
                    }
                }
            }
        }

        &:nth-child(2n + 2) {
            &:after {
                margin-left: -15px;

                @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                    margin-left: 0;
                }
            }

            .blog-posts__post-col {
                &:first-child {
                    padding-left: 50px;

                    @media only screen and (max-width: map-get($grid-breakpoints-max, xl)) {
                        padding-left: 15px;
                    }

                    @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
                        padding-left: 5px;
                    }
                }
            }
        }

        .blog-posts__post {
            padding-top: 70px;
            padding-bottom: 70px;

            @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                padding-top: 50px;
                padding-bottom: 50px;
            }

            &.blog-posts__post--standard {
                padding-top: 60px;
                padding-bottom: 60px;
                border-bottom: 1px solid $midlightgrey;

                .blog-posts__post-col {
                    padding-right: 15px;
                    padding-left: 15px;
                }
            }

            a {
                &:hover,
                &:focus {
                    .blog-posts__heading {
                        color: $blue;
                    }

                    .link {
                        color: $blue;
                    }
                }
            }
        }
    }

    &__thumbnail {
        text-align: right;

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            padding-bottom: 30px;
            text-align: center;

            img {
                width: 100%;
                max-width: 375px;
                margin: 0 auto;
            }
        }
    }

    &__heading {
        margin-bottom: 10px;
        transition: 300ms;
    }

    &__details {
        margin-bottom: 10px;
    }

    &__detail {
        display: inline-block;
        vertical-align: middle;
        font-family: $headingfontfamily;
        text-transform: uppercase;
        color: $blue;

        span {
            display: inline-block;
            vertical-align: middle;
        }

        &:last-child {
            &:after {
                display: none;
            }
        }

        &:after {
            content: "•";
            display: inline-block;
            margin: 0 5px 0;
        }
    }

    &__infinite-scroll {
        position: relative;
        text-align: center;

        .button {
            min-width: 230px;
            margin: 65px 0;

            &:disabled {
                opacity: 0;
            }
        }
    }

    &__excerpt {
        i.fas.fa-chevron-right {
            color: $teal !important;
            font-size: 0.6em !important;
        }
    }
}
