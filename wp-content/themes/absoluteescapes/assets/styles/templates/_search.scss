.search-results {
    
    a {
        font-weight: 400;
        text-decoration: none;

        &:hover, &:focus {
            text-decoration: none;

            .search-results__title {
                color: $blue;
            }

            .link {
                color: $blue;
            }
        }
    }

    &__content {
        padding: 100px 0 80px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            padding: 60px 0;
        }
    }

    &__posts {
        padding: 0;
    }

    &__post {
        padding: 40px 0;
        border-bottom: 1px solid $midlightgrey;

        &:last-child {
            border-bottom: none;
        }
    }

    &__title {
        transition: 300ms;
    }

    &__copy {
        i.fas.fa-chevron-right {
            color: $teal !important;
            font-size: 0.6em !important;
        }
    }
}