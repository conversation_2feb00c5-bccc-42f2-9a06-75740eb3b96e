.inspiration {

  

    &__inner {
        padding: 75px 0;


        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            padding: 45px 0;
        }
    }

    &__post {
        padding: 65px 0;
        border-bottom: 1px solid $midlightgrey;

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            padding: 45px 0;
        }

        a {
            font-weight: 400;
            text-decoration: none;

            &:hover, &:focus {
                .link {
                    color: $blue;
                }
            }
        }

        svg {
            color: #01a59f;
            height: 14px;
            margin-left: 2px;
        }

        &:last-child {
            border-bottom: none;
        }
    }

    &__post-row {
        align-items: center;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            align-items: flex-start;
        }
    }

    &__post-col {
        @media only screen and (max-width: 360px) {
            flex: 0 0 100%;
            max-width: 100%;
            &:nth-child(1) {
                order: 2;
            }
        }
    }

    &__heading {
        margin-bottom: 25px;
    }


    &__thumbnail {
        max-width: 225px;
        margin-left: auto;

        @media only screen and (max-width: 360px) {
            margin: 0 auto 20px;
        }
    }



    &__excerpt {
        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            display: none;
        }
    }
}