.route {

    &__inner {
        position: relative;
        z-index: 2;
        padding: 65px 0;

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            padding: 45px 0;
        }
    }


    &__container {
        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            padding: 0 20px;
        }
    }

    .accordion {
        margin: 0;

        &__heading {
            display: block;
            margin: 0;
            position: relative;
            font-size: 1.8rem;
            font-family: $headingfontfamily;
            font-weight: 400;
            color: $bluegrey;
        }

        &__heading-wrapper {
            position: relative;
        }

        i {
            position: absolute;
            top: 0;
            right: -15px;
            bottom: 0;
            margin: auto 0;
            color: $teal;
            transition: 300ms;
        }

        &__item {
            flex: 0 0 100%;
            max-width: 100%;
            padding: 17px 45px 17px 15px;
            border-bottom: 1px solid $midlightgrey;

            &:first-child {
                border-top: 1px solid $midlightgrey;
            }

            &:after {
                display: none;
            }

            &.active {
                i {
                    transform: rotate(-180deg);
                }
            }
        }
    }


    &__map-wrapper {
        padding: 50px 0;
        & iframe {
            border-width: 0;
        }
    }

    .acf-map {
        height: 425px;
    }

    iframe {
        width: 100%!important;
    }
}
