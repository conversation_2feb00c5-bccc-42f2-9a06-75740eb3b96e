.overview {
    position: relative;
    //background: #efefef;

    &:after {
        content: '';
        display: block;
        position: absolute;
        top: 0;
        left: -500%;
        z-index: 0;
        width: 1000%;
        height: 100%;
        // background: inherit;
    }

    &__inner {
        position: relative;
        z-index: 2;
        padding: 65px 0;

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            padding: 45px 0;
        }
    }

    &__container {
        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            padding: 0 20px;
        }
    }

    .marker__items {
        padding: 5px 0 0;
    }

    .marker__item {
        display: inline-block;
        vertical-align: middle;
        margin-right: 18px;

        &:last-child {
            margin-right: 0;
        }
    }

    &__points-list {
        padding: 30px 0 45px;
        margin: 0;
        list-style: none;
        column-count: 2;
    }

    &__points-item {

        font-family: $headingfontfamily;
        color: $bluegrey;

    }

    .accordion {
        margin-bottom: 30px;


    }


    &__copy {
        padding-bottom: 15px;

        &--read-more {
            display: none;
        }
    }

    li {
        margin-bottom: 10px;
    }

    &__bottom-copy-wrapper {
        margin-top: -10px;
    }

    &__bottom-copy-trigger {
        display: inline-block;
        margin-bottom: 10px;
        cursor: pointer;

        &.active {
            > svg {
                transform: rotate(180deg);
            }
        }
    }

}
