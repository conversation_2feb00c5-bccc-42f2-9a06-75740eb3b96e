.travel-info {

    &__inner {
        position: relative;
        z-index: 2;
        padding: 65px 0;

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            padding: 45px 0;
        }
    }


    &__container {
        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            padding: 0 20px;
        }
    }

    &__lists {
        display: flex;
        flex-wrap: wrap;
        padding-top: 25px;
        margin: 0 -15px;
    }

    &__list {
        flex: 0 0 100%;
        max-width: 100%;
        padding: 0 15px;
        margin-bottom: 60px;

        //@media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
        //    flex: 0 0 100%;
        //    max-width: 100%;
        //}
    }

    .accordion {
        margin: 0;

        &__heading {
            display: block;
            margin: 0;
            position: relative;
            font-size: 1.8rem;
            font-family: $headingfontfamily;
            font-weight: 400;
            color: $bluegrey;
        }

        svg, i {
            position: absolute;
            top: 0;
            right: -15px;
            bottom: 0;
            margin: auto 0;
            color: #01a59f;
            -webkit-transition: .3s;
            transition: .3s;
        }

        &__item {
            flex: 0 0 100%;
            max-width: 100%;
            padding: 17px 45px 17px 15px;
            border-bottom: 1px solid $midlightgrey;

            &:first-child {
                border-top: 1px solid $midlightgrey;
            }

            &:after {
                display: none;
            }


            &.active {
                svg {
                    transform: rotate(-180deg);
                }
            }
        }
    }
}
