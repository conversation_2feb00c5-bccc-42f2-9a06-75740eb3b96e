<?php

/**
 * Overview
 */

$navigation_label = seoUrl(get_sub_field('navigation_label'));
$highlights_heading = get_sub_field('highlights_heading');
$intro_copy = get_sub_field('intro_copy');
$bottom_copy = get_sub_field('copy');
$bg_colour = seoUrl(get_sub_field('background_colour'));

?>

<section id="<?php echo $navigation_label; ?>" class="overview <?php if($bg_colour) : ?>bg-<?php echo $bg_colour; ?><?php endif; ?>">
    <div class="overview__inner" data-aos="fade">
        <div class="overview__container">
            <div class="overview__content">
                <h2 class="overview__heading"><?php _e('Overview', 'absoluteescapes'); ?></h2>

                <?php

                $mtypes = get_the_terms(get_the_ID(), 'holiday-type');
                $mDays = get_field('holiday_minimum_duration');
                $mxDays = get_field('holiday_maximum_duration');
                if($mxDays == 999) {
                    $mxDays = '';
                }

                if($mDays === $mxDays) {
                    $mxDays = '';
                }

                $nights_days = get_field('holiday_day_night');
                $mDistance = get_field('holiday_distance');

                ?>

                <div class="marker__items">
                    <?php if($mtypes) : ?>
                        <div class="marker__item">
                            <?php foreach($mtypes as $type) : ?>
                                <?php

                                $icon = get_field('holiday_type_icon', $type);
                                $icon_fa = get_field('holiday_type_icon_fa', $type);
                                $colour = get_field('holiday_type_colour', $type);


                                if($type->parent) {
                                    continue;
                                }


                                ?>

                                <div class="holidays__type cat">
                                    <?php if($icon) : ?>
                                        <span class="holidays__icon cat__icon" style="background-color: <?php echo ($colour) ? $colour : '#3e5056'; ?>"><img src="<?php echo $icon['url']; ?>" alt="<?php echo $icon['alt']; ?>"></span>
                                    <?php else : ?>
                                        <?php if($icon_fa) : ?>
                                            <span class="holidays__icon cat__icon" style="background-color: <?php echo ($colour) ? $colour : '#3e5056'; ?>"><i class="<?php echo $icon_fa; ?>"></i></span>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                    <span class="holidays__type-text cat__text"><?php echo $type->name; ?></span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                    <?php if($mDays) : ?>
                        <div class="marker__item">
                            <div class="cat">
                                <span class="cat__icon" style="background-color: #3e5056;"><i class="fal fa-calendar-alt"></i></span>
                                <span class="cat__text"><?php echo $mDays ?><?php if($mxDays) : ?><?php echo ' - ' . $mxDays; ?><?php endif; ?><?php echo ' ' . $nights_days; ?></span>
                            </div>
                        </div>
                    <?php endif; ?>
                    <?php if($mDistance) : ?>
                        <div class="marker__item">
                            <div class="cat">
                                <span class="cat__icon" style="background-color: #3e5056;"><i class="fal fa-wave-sine"></i></span>
                                <span class="cat__text"><?php echo $mDistance; ?></span>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <?php if(have_rows('list')) : ?>
                    <div class="overview__points">
                        <ul class="overview__points-list">
                            <?php while(have_rows('list')) : the_row(); ?>
                                <?php

                                $item = get_sub_field('item');

                                ?>

                                <?php if($item) : ?>
                                    <li class="overview__points-item list-arrow"><?php echo $item; ?></li>
                                <?php endif; ?>

                            <?php endwhile; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <?php if($highlights_heading) : ?>
                    <h3 class="overview__subheading"><?php echo $highlights_heading; ?></h3>
                <?php endif; ?>

                <?php if(have_rows('highlights')) : ?>
                    <div class="overview__highlights flex accordion">
                        <?php while(have_rows('highlights')) : the_row(); ?>
                            <?php

                            $heading = get_sub_field('heading');
                            $image = wp_get_attachment_image(get_sub_field('image'), 'accordion');
                            $copy = get_sub_field('copy');

                            ?>

                            <div class="overview__highlight accordion__item">
                                <div class="overview__highlight-heading-wrapper flex accordion__heading-wrapper">
                                    <?php if($image) : ?>
                                        <div class="overview__highlight-image-wrapper accordion__image-wrapper">
                                            <?php echo $image; ?>
                                        </div>
                                    <?php endif; ?>
                                    <div class="overview__highlight-heading-text accordion__heading-text">
                                        <?php if($heading) : ?>
                                            <h5 class="overview__highlight-heading accordion__heading"><?php echo $heading; ?></h5>
                                        <?php endif; ?>

                                        <span class="overview__link link"><i class="fas fa-chevron-down"></i></span>
                                    </div>
                                </div>
                                <?php if($copy) : ?>
                                <div class="overview__highlight-copy-wrapper none accordion__copy-wrapper">

                                    <div class="overview__highlight-copy accordion__copy content-area">
                                        <?php echo $copy; ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>

                        <?php endwhile; ?>
                    </div>
                <?php endif; ?>
                <?php if($intro_copy) : ?>
                    <div class="overview__copy copy-large">
                        <?php echo $intro_copy; ?>
                    </div>
                <?php endif; ?>
                <?php if($bottom_copy) : ?>
                    <span class="link overview__bottom-copy-trigger"><span class="link-text"><?php _e('Read more'); ?></span> <i class="fas fa-chevron-down"></i></span>
                    <div class="overview__copy overview__copy--read-more content-area">
                        <?php echo $bottom_copy; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>
