<?php

/**
 * Route
 */

$navigation_label = seoUrl(get_sub_field('navigation_label'));
$heading = get_sub_field('heading');
$copy = get_sub_field('copy');
$route_map = get_sub_field('route_map');
$itinerary_heading = get_sub_field('itinerary_heading');
$itinerary_copy = get_sub_field('itinerary_copy');
$bg_colour = seoUrl(get_sub_field('background_colour'));

?>

<section id="<?php echo $navigation_label; ?>" class="route <?php if($bg_colour) : ?>bg-<?php echo $bg_colour; ?><?php endif; ?>">
    <div class="route__inner" data-aos="fade">
        <div class="route__container">
            <?php if($heading) : ?>
                <h2 class="route__heading"><?php echo $heading; ?></h2>
            <?php endif; ?>
            <?php if($copy) : ?>
                <div class="route__copy content-area">
                    <?php echo $copy; ?>
                </div>
            <?php endif; ?>

            <?php if($route_map) : ?>
                <div class="route__map-wrapper">
                    <?php echo $route_map; ?>
                </div>
            <?php endif; ?>

            <?php if($itinerary_heading) : ?>
                <h3 class="route__heading"><?php echo $itinerary_heading; ?></h3>
            <?php endif; ?>

            <?php if($itinerary_copy) : ?>
                <div class="route__copy content-area">
                   <?php echo $itinerary_copy; ?>
                </div>
            <?php endif; ?>

            <?php if(have_rows('itinerary')) : ?>
                <div class="flex accordion">
                    <?php while(have_rows('itinerary')) : the_row(); ?>
                        <?php

                        $heading = get_sub_field('heading');
                        $image = wp_get_attachment_image(get_sub_field('image'), 'accordion');
                        $copy = get_sub_field('copy');

                        ?>

                        <div class="accordion__item">
                            <div class="accordion__heading-wrapper">
                                <?php if($image) : ?>
                                    <div class="accordion__image-wrapper">
                                        <?php echo $image; ?>
                                    </div>
                                <?php endif; ?>
                                <span class="accordion__heading"><?php echo $heading; ?> <i class="fas fa-chevron-down"></i></span>
                            </div>
                            <?php if($copy) : ?>
                            <div class="none accordion__copy-wrapper">
                                <div class="accordion__copy content-area">
                                    <?php echo $copy; ?>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>

                    <?php endwhile; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section><!-- .route -->

<script type="text/javascript">
(function($) {

/*
*  new_map
*
*  This function will render a Google Map onto the selected jQuery element
*
*  @type	function
*  @date	8/11/2013
*  @since	4.3.0
*
*  @param	$el (jQuery element)
*  @return	n/a
*/

function new_map( $el ) {

    // var
    var $markers = $el.find('.marker');


    // vars
    var args = {
        zoom		: 16,
        center		: new google.maps.LatLng(0, 0),
        mapTypeId	: google.maps.MapTypeId.ROADMAP,
        disableDefaultUI: true
    };


    // create map
    var map = new google.maps.Map( $el[0], args);


    // add a markers reference
    map.markers = [];


    // add markers
    $markers.each(function(){

        add_marker( $(this), map );

    });


    // center map
    center_map( map );


    // return
    return map;

}

/*
*  add_marker
*
*  This function will add a marker to the selected Google Map
*
*  @type	function
*  @date	8/11/2013
*  @since	4.3.0
*
*  @param	$marker (jQuery element)
*  @param	map (Google Map object)
*  @return	n/a
*/

function add_marker( $marker, map ) {

    // var
    var latlng = new google.maps.LatLng( $marker.attr('data-lat'), $marker.attr('data-lng') );

    var icon = '<?php echo get_stylesheet_directory_uri() . '/dist/img/marker.png'; ?>';

    // create marker
    var marker = new google.maps.Marker({
        position	: latlng,
        map			: map,
        icon        : icon
    });

    // add to array
    map.markers.push( marker );

    // if marker contains HTML, add it to an infoWindow
    if( $marker.html() )
    {

            var infowindow = new InfoBox({
                content: $marker.html()
            });

            var myOptions = {
                content: $marker.html(),
                disableAutoPan: true,

                pixelOffset: new google.maps.Size(0, 0),
                zIndex: null,
                boxStyle: {
                opacity: 1,
                },
                closeBoxMargin: "0 0 0 0",

                infoBoxClearance: new google.maps.Size(1, 1),
                isHidden: false,
                pane: "floatPane",
                enableEventPropagation: false
        };

        // create info window
        // var infowindow = new google.maps.InfoWindow({
        //     content		: $marker.html()
        // });

        // show info window when marker is clicked
            google.maps.event.addListener(marker, 'click', function() {

        //     infowindow.open( map, marker );
                infowindow.setOptions(myOptions);
                infowindow.open(map,marker);
            });



    }

}

/*
*  center_map
*
*  This function will center the map, showing all markers attached to this map
*
*  @type	function
*  @date	8/11/2013
*  @since	4.3.0
*
*  @param	map (Google Map object)
*  @return	n/a
*/

function center_map( map ) {

    // vars
    var bounds = new google.maps.LatLngBounds();

    // loop through all markers and create bounds
    $.each( map.markers, function( i, marker ){

        var latlng = new google.maps.LatLng( marker.position.lat(), marker.position.lng() );

        bounds.extend( latlng );

    });

    // only 1 marker?
    if( map.markers.length == 1 )
    {
        // set center of map
        map.setCenter( bounds.getCenter() );
        map.setZoom( 16 );
    }
    else
    {
        // fit to bounds
        map.fitBounds( bounds );
    }

    $('#mapTab').on('click', function() {

        $('.holidays-results__tab').removeClass('active');
        $(this).addClass('active');

        $('.holidays-results__map').addClass('active');
        $('.holidays-results__posts').removeClass('active');

        map.fitBounds( bounds );


        });

        $('#listTab').on('click', function() {

        $('.holidays-results__tab').removeClass('active');
        $(this).addClass('active');

        $('.holidays-results__map').removeClass('active');
        $('.holidays-results__posts').addClass('active');

    });

}



/*
*  document ready
*
*  This function will render each map when the document is ready (page has loaded)
*
*  @type	function
*  @date	8/11/2013
*  @since	5.0.0
*
*  @param	n/a
*  @return	n/a
*/
// global var
var map = null;

$(document).ready(function(){

    $('.acf-map').each(function(){

        // create map
        map = new_map( $(this) );


    });

});

})(jQuery);
</script>
