<?php

/**
 * Steps Ahead
 * Content comes from Theme Settings, individual blocks control styling only
 */

// Get global content from Theme Settings
$global_content = get_field('steps_ahead_global_content', 'option');
if (!$global_content || !$global_content['panel_status']) {
    return;
}

// Check if block is enabled (for flexible content blocks)
$panel_status = get_sub_field('panel_status');
if (!$panel_status) {
    return;
}

// Use global content for title and columns
$heading = $global_content['panel_title'] ?: '';
$columns = $global_content['columns'];

// Use individual block settings for styling - use consistent field names
$background_color = get_sub_field('panel_background');
$curved_edge = get_sub_field('curve_position');
$flip_curved_edge = get_sub_field('curve_flip');
$point_title_color = get_sub_field('point_title') ?: '#75ada4';
$column_background_color = get_sub_field('point_background');

// Set up background classes
$background_classes = '';
$has_background = $background_color && $background_color !== 'transparent' && $background_color !== '';
if ($has_background) {
    $background_classes .= ' has-background';
}
if ($curved_edge && $curved_edge !== 'none') {
    $background_classes .= ' curve-' . $curved_edge;
    if ($flip_curved_edge) {
        $background_classes .= ' curve-flipped';
    }
}

?>

<section class="steps-ahead">
    <div class="steps-ahead__inner<?php echo $background_classes; ?>" data-aos="fade"<?php if ($has_background) : ?> style="background-color: <?php echo $background_color; ?>;"<?php endif; ?>>
        <div class="container steps-ahead__container">
            <?php if($heading) : ?>
                <div class="steps-ahead__content centre inner-container">
                    <h2 class="steps-ahead__heading h2-large heading-light heading-underlined text-weight-regular"><?php echo esc_html($heading); ?></h2>
                </div>
            <?php endif; ?>

            <?php if($columns && !empty($columns)) : ?>
                <div class="row steps-ahead__row">
                    <?php foreach($columns as $column) : ?>
                        <?php
                        $ctitle = $column['title'] ?: '';
                        $cimage = $column['image'] ?: null;
                        $cpoints = $column['points'] ?: [];
                        ?>

                        <div class="col-md-3 steps-ahead__col">
                            <div class="steps-ahead__col-content centre"<?php if($column_background_color) : ?> style="background-color: <?php echo $column_background_color; ?>;"<?php endif; ?>>
                                <?php if($cimage) : ?>
                                    <div class="steps-ahead__col-image">
                                        <?php
                                        // Use WordPress built-in responsive image with srcset
                                        $image_id = $cimage['ID'];
                                        $image_url = wp_get_attachment_image_url($image_id, 'full');
                                        $image_metadata = wp_get_attachment_metadata($image_id);

                                        // Build srcset from metadata
                                        $srcset_array = array();
                                        if ($image_metadata && isset($image_metadata['sizes'])) {
                                            $base_url = dirname($image_url) . '/';

                                            // Add original image
                                            $srcset_array[] = $image_url . ' ' . $image_metadata['width'] . 'w';

                                            // Add thumbnail sizes
                                            foreach ($image_metadata['sizes'] as $size_name => $size_data) {
                                                if (isset($size_data['width']) && isset($size_data['file'])) {
                                                    $size_url = $base_url . $size_data['file'];
                                                    $srcset_array[] = $size_url . ' ' . $size_data['width'] . 'w';
                                                }
                                            }
                                        }
                                        $srcset = implode(', ', $srcset_array);
                                        ?>
                                        <img src="<?php echo esc_url($image_url); ?>"
                                             srcset="<?php echo esc_attr($srcset ?: $image_url); ?>"
                                             sizes="(max-width: 480px) 100vw, (max-width: 991px) 50vw, 25vw"
                                             alt="<?php echo esc_attr($cimage['alt'] ?: ''); ?>"
                                             style="width: 100%; height: 100%; object-fit: cover;">
                                        <?php if($ctitle) : ?>
                                            <h4 class="steps-ahead__col-heading"><?php echo esc_html($ctitle); ?></h4>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>

                                <?php if($cpoints && !empty($cpoints)) : ?>
                                    <div class="steps-ahead__col-points">
                                        <?php foreach($cpoints as $point) : ?>
                                            <?php
                                            $point_title = $point['title'] ?: '';
                                            $point_text = $point['text'] ?: '';
                                            ?>
                                            <?php if($point_title || $point_text) : ?>
                                                <div class="steps-ahead__point">
                                                    <?php if($point_title) : ?>
                                                        <h5 class="steps-ahead__point-title" style="color: <?php echo $point_title_color; ?>;"><?php echo esc_html($point_title); ?></h5>
                                                    <?php endif; ?>
                                                    <?php if($point_text) : ?>
                                                        <div class="steps-ahead__point-text">
                                                            <?php echo nl2br(esc_html($point_text)); ?>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section><!-- .steps-ahead -->