<?php

/**
 * Functions
 *
 * This file contains general functions
 *
 * <AUTHOR> Lane Agency
 * @copyright 2019 The Lane Agency
 * @version   1.0
 */

define('THEME_DIRECTORY', get_template_directory());

/*
Body classes
- add more classes to the body to enable more specific targeting if needed
 */
function ambrosite_body_class($classes)
{
    $post_name_prefix = 'postname-';
    $page_name_prefix = 'pagename-';
    $single_term_prefix = 'single-';
    $single_parent_prefix = 'parent-';
    $category_parent_prefix = 'parent-category-';
    $term_parent_prefix = 'parent-term-';
    $site_prefix = 'site-';
    global $wp_query;
    if (is_single()) {
        $wp_query->post = $wp_query->posts[0];
        setup_postdata($wp_query->post);
        $classes[] = $post_name_prefix . $wp_query->post->post_name;
        $taxonomies = array_filter(get_post_taxonomies($wp_query->post->ID), "is_taxonomy_hierarchical");
        foreach ($taxonomies as $taxonomy) {
            $tax_name = ($taxonomy != 'category') ? $taxonomy . '-' : '';
            $terms = get_the_terms($wp_query->post->ID, $taxonomy);
            if ($terms) {
                foreach ($terms as $term) {
                    if (!empty($term->slug)) $classes[] = $single_term_prefix . $tax_name . sanitize_html_class($term->slug, $term->term_id);
                    while ($term->parent) {
                        $term = get_term($term->parent, $taxonomy);
                        if (!empty($term->slug)) $classes[] = $single_parent_prefix . $tax_name . sanitize_html_class($term->slug, $term->term_id);
                    }
                }
            }
        }
    } elseif (is_archive()) {
        if (is_category()) {
            $cat = $wp_query->get_queried_object();
            while ($cat->parent) {
                $cat = get_category($cat->parent);
                if (!empty($cat->slug)) $classes[] = $category_parent_prefix . sanitize_html_class($cat->slug, $cat->cat_ID);
            }
        } elseif (is_tax()) {
            $term = $wp_query->get_queried_object();
            while ($term->parent) {
                $term = get_term($term->parent, $term->taxonomy);
                if (!empty($term->slug)) $classes[] = $term_parent_prefix . sanitize_html_class($term->slug, $term->term_id);
            }
        }
    } elseif (is_page()) {
        $wp_query->post = $wp_query->posts[0];
        setup_postdata($wp_query->post);
        $classes[] = $page_name_prefix . $wp_query->post->post_name;
    }
    if (is_multisite()) {
        global $blog_id;
        $classes[] = $site_prefix . $blog_id;
    }
    return $classes;
}

add_filter('body_class', 'ambrosite_body_class');

/*
Disable the theme editor
- stop clients from breaking their website
 */
define('DISALLOW_FILE_EDIT', true);

/*
Remove version info
- makes it that little bit harder for hackers
 */
function complete_version_removal()
{
    return '';
}

add_filter('the_generator', 'complete_version_removal');

/*
Remove info from headers
- remove the stuff we don't need
 */
remove_action('wp_head', 'rsd_link');
remove_action('wp_head', 'wp_generator');
remove_action('wp_head', 'feed_links', 2);
remove_action('wp_head', 'index_rel_link');
remove_action('wp_head', 'wlwmanifest_link');
remove_action('wp_head', 'feed_links_extra', 3);
remove_action('wp_head', 'start_post_rel_link', 10, 0);
remove_action('wp_head', 'parent_post_rel_link', 10, 0);
remove_action('wp_head', 'adjacent_posts_rel_link', 10, 0);
remove_action('wp_head', 'print_emoji_detection_script', 7);
remove_action('wp_print_styles', 'print_emoji_styles');

/*
Excerpt
- this theme supports excerpts
 */
add_post_type_support('page', 'excerpt');

function new_excerpt_more($more)
{
    global $post;
    return '...';
}

function excerpt($limit)
{
    $excerpt = explode(' ', get_the_excerpt(), $limit);
    if (count($excerpt) >= $limit) {
        array_pop($excerpt);
        $excerpt = implode(" ", $excerpt) . '...';
    } else {
        $excerpt = implode(" ", $excerpt);
    }
    $excerpt = preg_replace('`\[[^\]]*\]`', '', $excerpt);
    return $excerpt;
}

function limit_content($limit)
{
    $content = explode(' ', get_the_content(), $limit);
    if (count($content) >= $limit) {
        array_pop($content);
        $content = implode(" ", $content) . '...';
    } else {
        $content = implode(" ", $content);
    }
    $content = preg_replace('`\[[^\]]*\]`', '', $content);
    return $content;
}

add_action( 'after_setup_theme', 'image_sizes' );

function image_sizes() {

    add_theme_support('post-thumbnails');
    add_image_size('full', 3000, 3000, true);
    add_image_size('holiday_type', 225, 310, true);
    add_image_size('holiday_type_large', 768, 1057, true); // orig: 225 x 310px
    add_image_size('holiday_type_small', 225, 165, true);
    add_image_size('destination', 380, 235, true);
    add_image_size('gram', 315, 315, true);
    add_image_size('menu_thumb', 70, 50, true);
    add_image_size('carousel', 800, 430, true);
    add_image_size('carousel-small', 700, 404, true);
    add_image_size('recommend', 690, 450, true);
    add_image_size('accordion', 103, 69, true);
    add_image_size('tiny', 16, 16, false);
    add_image_size('small', 700, 1152, true);
    add_image_size('author_avatar', 150, 150, true);

}



/*
Scripts & Styles
- include the scripts and stylesheets
 */
add_action('wp_enqueue_scripts', 'script_enqueues');

add_filter('gform_init_scripts_footer', function () {
    return true;
});

function script_enqueues()
{

    if (wp_script_is('jquery', 'registered')) {

        wp_deregister_script('jquery');

    }

    //wp_enqueue_script('jquery', 'https://ajax.googleapis.com/ajax/libs/jquery/2.2.4/jquery.min.js', array(), '2.2.4', false);
    wp_enqueue_script('jquery', 'https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js', array(), '3.3.1', false);
    wp_enqueue_script('vendor', get_template_directory_uri() . '/dist/scripts/vendor.min.js', array(), '1.0.1', true);

    wp_enqueue_script(
      'custom',
      get_template_directory_uri() . '/dist/scripts/main.min.js',
      array(),
      filemtime( get_template_directory() . '/dist/scripts/main.min.js' ),
      true
    );

    wp_enqueue_style('style',
      get_template_directory_uri() . '/dist/styles/style.min.css',
      false,
      filemtime( get_template_directory() . '/dist/styles/style.min.css' ),
      'all'
    );

    wp_localize_script('custom', 'theme_params', array(
        'ajaxurl' => admin_url('admin-ajax.php'), // WordPress AJAX
        'stylesheet_dir' => get_stylesheet_directory_uri(),
    ));

}

/*
Admin Bar
- hide the admin bar
 */
add_filter('show_admin_bar', '__return_false');

/*
Menus
- enable WordPress Menus
 */
if (function_exists('register_nav_menus')) {
    register_nav_menus(array('header' => __('Main Nav'), 'header-secondary' => __('Secondary Nav'), 'header-mobile' => __('Header Mobile'), 'footer' => __('Footer Nav')));
}

/*
Menu Classes
- add first and last to menu items
 */
function wpdev_first_and_last_menu_class($items)
{
    $items[1]->classes[] = 'first';
    $items[count($items)]->classes[] = 'last';
    return $items;
}

add_filter('wp_nav_menu_objects', 'wpdev_first_and_last_menu_class');

/*
Admin Menus
- hide unused menu items
 */
function remove_menus()
{

    remove_menu_page('edit-comments.php');

}

add_action('admin_menu', 'remove_menus');

//Add svg upload to wordpress

function cc_mime_types($mimes)
{
    $mimes['svg'] = 'image/svg+xml';
    return $mimes;
}

add_filter('upload_mimes', 'cc_mime_types');


add_action('admin_init', 'remove_textarea');

function remove_textarea()
{
    remove_post_type_support('page', 'editor');
    remove_post_type_support('post', 'editor');
}


/* Yoast Breadcrumbs
==================== */
add_theme_support('yoast-seo-breadcrumbs');



// Move Yoast to bottom
function yoasttobottom() {
	return 'low';
}
add_filter( 'wpseo_metabox_prio', 'yoasttobottom');

/* Google Map ACF
================= */

function my_acf_init()
{

    acf_update_setting('google_api_key', get_field('google_maps_api_key', 'options'));
}

add_action('acf/init', 'my_acf_init');




/* Gravity Forms
================ */

// add_filter('gform_ajax_spinner_url', 'spinner_url', 10, 2);
// function spinner_url($image_src, $form)
// {
//     return get_stylesheet_directory_uri() . '/dist/img/ajax-spinner.png';
// }

add_filter('gform_confirmation_anchor', '__return_false');

/* Get Post Content
=================== */

function get_content_by_id($id)
{
    $content_post = get_post($id);
    $content = $content_post->post_content;
    $content = apply_filters('the_content', $content);
    $content = str_replace(']]>', ']]&gt;', $content);
    return $content;
}

/**
 * Define custom ACF JSON save and load points
 */
add_filter('acf/settings/save_json', 'custom_json_save_point');
function custom_json_save_point($path)
{
  return get_stylesheet_directory() . '/lib/acf-json';
}

add_filter('acf/settings/load_json', 'custom_json_load_point');
function custom_json_load_point($paths)
{
  unset($paths[0]);
  $paths[] = get_stylesheet_directory() . '/lib/acf-json';
  return $paths;
}

/*
AFC Options
- register the AFC options
 */
if (function_exists('acf_add_options_page')) {

    acf_add_options_page(array(
        'page_title' => 'Theme Settings',
        'menu_title' => 'Theme Settings',
        'menu_slug' => 'theme-settings',
        'capability' => 'edit_posts',
        'redirect' => false
    ));

}

/**
 * Populate ACF select field options with Gravity Forms forms
 */
function acf_populate_gf_forms_ids( $field ) {
  if ( class_exists( 'GFFormsModel' ) ) {
    $choices = [];

    foreach ( \GFFormsModel::get_forms() as $form ) {
      $choices[ $form->id ] = $form->title;
    }

    $field['choices'] = $choices;
  }

  return $field;
}
add_filter( 'acf/load_field/name=gravity_forms', 'acf_populate_gf_forms_ids' );

add_filter('human_time_diff', function ($since, $diff, $from, $to) {

    // The issue might be that "mins" is being replaced with "minutes"
    // but then "min" (inside "minutes") is being replaced with "minute"
    // causing "minutes" -> "minuteutes"

    // Fix: Use more specific replacements to avoid double-processing
    $since = preg_replace('/\b(\d+)\s*mins?\b/', '$1 minutes', $since);

    // Then extract number and convert to words
    preg_match_all('!\d+!', $since, $match);

    if (!empty($match[0])) {
        $number = implode('', $match[0]);
        $number_text = convert_number_to_words($number);

        // Replace the number with the word version
        return preg_replace('/\d+/', $number_text, $since);
    }

    return $since;

}, 10, 4);

/* Return Unique Post Dates
=========================== */

function unique_post_dates($post)
{

    $args = array(
        'posts_per_page' => -1,
        'post_type' => $post
    );

    $posts = get_posts($args);

    $dates = array();

    foreach ($posts as $p) {

        $date_form = strtotime($p->post_date);

        array_push($dates, date('F, Y', $date_form));
    }

    $dates_unique = array_unique($dates);

    return $dates_unique;

}

/**
 *
 * Get Loop Term names
 *
 * Return term array
 *
 */

function prentice_unique_term_names()
{

    $product_cat = get_query_var('product_cat');
    $product_tag = get_query_var('product_tag');

    $args = array(
        'limit' => -1,
        'posts_per_page' => -1
    );

    if (is_product_category()) {
        $args['product_cat'] = $product_cat;
    }

    if (is_product_tag()) {
        $args['product_tag'] = $product_tag;
    }

    if (isset($_GET['s']) && $_GET['s'] !== '') {
        $args['s'] = get_search_query();
    }

    $products = wc_get_products($args);

    if (empty($products)) {
        return '';
    }

    $term_arr = [];

    foreach ($products as $product) {
        $term_arr[] = get_the_terms($product->get_id(), 'product_tag');
    }

    $term_arr_unique = array_unique(wp_list_pluck(array_merge(...$term_arr), 'name'));

    return $term_arr_unique;

}

function getPostViews($postID){
    $count_key = 'post_views_count';
    $count = get_post_meta($postID, $count_key, true);
    if($count==''){
        delete_post_meta($postID, $count_key);
        add_post_meta($postID, $count_key, '0');
        return "0 View";
    }
    return $count.' Views';
}
function setPostViews($postID) {
    $count_key = 'post_views_count';
    $count = get_post_meta($postID, $count_key, true);
    if($count==''){
        $count = 0;
        delete_post_meta($postID, $count_key);
        add_post_meta($postID, $count_key, '0');
    }else{
        $count++;
        update_post_meta($postID, $count_key, $count);
    }
}

// Remove issues with prefetching adding extra views
remove_action( 'wp_head', 'adjacent_posts_rel_link_wp_head', 10, 0);

add_filter('manage_posts_columns', 'posts_column_views');
add_action('manage_posts_custom_column', 'posts_custom_column_views',5,2);
function posts_column_views($defaults){
    $defaults['post_views'] = __('Views');
    return $defaults;
}
function posts_custom_column_views($column_name, $id){
    if($column_name === 'post_views'){
        echo getPostViews(get_the_ID());
    }
}

function holiday_posts( $query ) {


    if ($query->is_tax() || is_post_type_archive('holiday') && $query->is_main_query() && !is_admin()) {


    if(isset($_GET['durationmin']) && isset($_GET['durationmin'])) {

        $meta_query = array(

            array(
                'key'     => 'holiday_maximum_duration',
                'type'    => 'NUMERIC',
                'value'   => $_GET['durationmin'],
                'compare' => '>=',
            ),
            array(
                'key'     => 'holiday_minimum_duration',
                'type'    => 'NUMERIC',
                'value'   => $_GET['durationmax'],
                'compare' => '<=',
            )
        );

        $query->set( 'meta_query', $meta_query );

    }

    $tax_query = $query->tax_query;

    if (isset($tax_query->queries) && is_array($tax_query->queries)) {
        $tax_query = $tax_query->queries;
    } else {
        $tax_query = array();
    }

    if(isset($_GET['region']) || isset($_GET['type'])) {

        // $tax_query = array();

        if(isset($_GET['region'])) {
            $tax_region = array(
                'taxonomy' => 'holiday-regions',
                'field'    => 'slug',
                'terms'    => $_GET['region'],
            );

            array_push($tax_query, $tax_region);
        }


        if(isset($_GET['type'])) {
            $tax_type = array(
                'taxonomy' => 'holiday-type',
                'field'    => 'slug',
                'terms'    => $_GET['type'],
            );

            array_push($tax_query, $tax_type);
        }

        $query->set( 'tax_query', $tax_query );
    }

    $current_sort = 'most-popular';
    if (isset($_GET['sort'])) {
        $current_sort = $_GET['sort'];
    }

    switch ($current_sort) {
        case 'most-popular':
            $query->set('meta_key', 'post_views_count');
            $query->set('orderby', 'meta_value_num');
            $query->set('order', 'DESC');
            break;
        case 'price-lowest':
            $query->set('meta_key', 'sort_price');
            $query->set('orderby', 'meta_value_num');
            $query->set('order', 'ASC');
            break;
        case 'price-highest':
            $query->set('meta_key', 'sort_price');
            $query->set('orderby', 'meta_value_num');
            $query->set('order', 'DESC');
            break;
        case 'shortest-duration':
            $query->set('meta_key', 'holiday_minimum_duration');
            $query->set('orderby', 'meta_value_num');
            $query->set('order', 'ASC');
            break;
        case 'longest-duration':
            $query->set('meta_key', 'holiday_maximum_duration');
            $query->set('orderby', 'meta_value_num');
            $query->set('order', 'DESC');
            break;
    }

}
}
//add_action('pre_get_posts','holiday_posts');


add_filter( 'gform_pre_render', 'populate_holidays' );
add_filter( 'gform_pre_validation', 'populate_holidays' );
add_filter( 'gform_pre_submission_filter', 'populate_holidays' );
add_filter( 'gform_admin_pre_render', 'populate_holidays' );
function populate_holidays( $form ) {

    foreach ( $form['fields'] as &$field ) {


        if ( $field->type != 'select' || strpos( $field->cssClass, 'populate-holidays' ) === false ) {
            continue;
        }
            // you can add additional parameters here to alter the posts that are retrieved
            // more info: http://codex.wordpress.org/Template_Tags/get_posts
            $posts = get_posts( 'numberposts=-1&post_status=publish&post_type=holiday&orderby=title' );

            $choices = array();

            foreach ( $posts as $post ) {
                $choices[] = array( 'text' => $post->post_title, 'value' => $post->post_title );
            }

            // update 'Select a Post' to whatever you'd like the instructive option to be
            $field->placeholder = 'Holiday';
            $field->choices = $choices;


    }

    return $form;
}

add_filter( 'gform_pre_render', 'populate_types' );
add_filter( 'gform_pre_validation', 'populate_types' );
add_filter( 'gform_pre_submission_filter', 'populate_types' );
add_filter( 'gform_admin_pre_render', 'populate_types' );
function populate_types( $form ) {

    foreach ( $form['fields'] as &$field ) {


        if ( $field->type != 'select' || strpos( $field->cssClass, 'populate-types' ) === false ) {
            continue;
        }
        // you can add additional parameters here to alter the posts that are retrieved
        // more info: http://codex.wordpress.org/Template_Tags/get_posts
        $types = get_terms( 'holiday-type' );

        $choices = array();

        foreach ( $types as $type ) {

            if($type->parent) {
                continue;
            }

            $choices[] = array( 'text' => $type->name, 'value' => $type->name );
        }

        // update 'Select a Post' to whatever you'd like the instructive option to be
        $field->placeholder = 'Type of holiday';
        $field->choices = $choices;




    }

    return $form;
}

add_filter( 'gform_pre_render', 'populate_country' );
add_filter( 'gform_pre_validation', 'populate_country' );
add_filter( 'gform_pre_submission_filter', 'populate_country' );
add_filter( 'gform_admin_pre_render', 'populate_country' );
function populate_country( $form ) {

    foreach ( $form['fields'] as &$field ) {


        if ( $field->type != 'select' || strpos( $field->cssClass, 'populate-region' ) === false ) {
            continue;
        }
        // you can add additional parameters here to alter the posts that are retrieved
        // more info: http://codex.wordpress.org/Template_Tags/get_posts
        $regions = get_terms( 'taxonomy=holiday-regions&parent=0' );

        $choices = array();

        foreach ( $regions as $region ) {
            $choices[] = array( 'text' => $region->name, 'value' => $region->name );
        }

        // update 'Select a Post' to whatever you'd like the instructive option to be
        $field->placeholder = 'Country';
        $field->choices = $choices;




    }

    return $form;
}


add_filter( 'gform_field_value_self_drive_check', 'my_custom_population_function' );
function my_custom_population_function( $value ) {

    $value = 'No';

    if(isset($_GET['selfdrive'])) {
        $value = $_GET['selfdrive'];
    }

    return $value;
}


function super_unique($array)
{
    $result = array_map("unserialize", array_unique(array_map("serialize", $array)));

    foreach ($result as $key => $value) {
        if (is_array($value)) {
            $result[$key] = super_unique($value);
        }
    }

    return $result;
}