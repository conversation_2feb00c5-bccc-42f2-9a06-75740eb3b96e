<?php

/**
 * Blog
 */

get_template_part('lib/blocks/page-header');

get_template_part('lib/components/categories');

$paged = (get_query_var('paged')) ? get_query_var('paged') : 1;

?>


<section id="blogPosts" class="blog-posts">
    <div class="blog-posts__inner">
        <div class="container blog-posts__container">
            <?php if (have_posts()) : $i = 0; ?>
                <div class="row blog-posts__row inf-posts">
                    <?php while (have_posts()) : the_post(); ?>
                        <div class="col-12 blog-posts__col--standard blog-posts__col inf-post">
                            <div class="blog-posts__post blog-posts__post--standard">
                                <a href="<?php echo get_the_permalink(); ?>">
                                    <div class="row blog-posts__post-row">
                                        <div class="col-md-9 blog-posts__post-col">
                                            <a href="<?php echo get_the_permalink(); ?>"><h3
                                                        class="blog-posts__heading"><?php echo get_the_title(); ?></h3>

                                            <?php if (has_excerpt()) : ?>
                                                <div class="blog-posts__excerpt content-area">
                                                    <p><?php echo excerpt(50); ?> <i class="fas fa-chevron-right"></i></p>
                                                </div>
                                            <?php endif; ?>
                                            </a>
                                        </div>
                                        <div class="col-md-3 blog-posts__post-col">
                                            <div class="blog-posts__thumbnail">
                                                <a href="<?php echo get_the_permalink(); ?>">
                                                    <?php echo wp_get_attachment_image(get_field('post_listing_image'), 'holiday_type_small'); ?>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        </div>
                        <?php $i++; endwhile;
                    wp_reset_postdata(); ?>
                </div>

                <?php if (get_next_posts_link()) : ?>
                    <div class="blog-posts__pagination">
                        <div class="blog-posts__infinite-scroll">
                            <button type="button" class="button button--alt button--link-plain button-inf"><span
                                        class="text"><?php _e('Load more', 'classroommonitor'); ?></span> <i
                                        class="fas fa-chevron-down"></i></button>
                            <div class="page-load-status"><span class="loader infinite-scroll-request"></span></div>
                        </div>
                        <div class="next-posts-link"><?php next_posts_link('Next page'); ?></div>
                    </div>
                <?php endif; ?>
            <?php else : ?>

                <div class="no-results centre">
                    <h3 class="text-uppercase"><strong>No Results Found.</strong></h3>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section><!-- .blog -->


<?php

get_template_part('lib/blocks/instagram');

?>
