<?php

/**
 * Blog Post
 */

get_template_part('lib/blocks/page-header');

get_template_part('lib/components/breadcrumbs');

?>

<section class="blog-post">
    <div class="blog-post__inner">
        <div class="container blog-post__container">
            <div class="blog-post__content inner-container">
                <div class="inner-wrapper">
                    <?php // get_template_part('lib/components/share'); ?>
                    <div class="blog-post__details">
                        <?php
                        $author_id = get_the_author_meta('ID');
                        $has_avatar = false;

                        // Check if user has a local avatar or Gravatar
                        if (function_exists('get_simple_local_avatar_url')) {
                            $has_avatar = !empty(get_simple_local_avatar_url($author_id, 75));
                        }

                        // If no local avatar, check for Gravatar (but avoid default/mystery avatars)
                        if (!$has_avatar) {
                            $avatar_url = get_avatar_url($author_id, array('size' => 75, 'default' => '404'));
                            $has_avatar = $avatar_url && !strpos($avatar_url, '404');
                        }
                        ?>

                        <div class="blog-post__author-info <?php echo $has_avatar ? 'has-avatar' : 'no-avatar'; ?>">
                            <?php if ($has_avatar): ?>
                                <?php
                                echo get_avatar($author_id, 75, '', get_the_author(), array(
                                    'class' => 'blog-post__author-avatar'
                                ));
                                ?>
                            <?php endif; ?>
                            <div class="blog-post__author-text">
                                <span class="blog-post__detail"><span><?php echo get_the_author(); ?></span></span>
                                <span class="blog-post__detail"><span><?php echo get_the_date('F j Y'); ?></span></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="sidebar__spacer"></div>
            </div>
        </div>
    </div>
    <?php
    get_template_part('lib/blocks/page-builder');
    ?>
</section>

