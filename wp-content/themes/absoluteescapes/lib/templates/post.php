<?php

/**
 * Blog Post
 */

get_template_part('lib/blocks/page-header');

get_template_part('lib/components/breadcrumbs');

?>

<section class="blog-post">
    <div class="blog-post__inner">
        <div class="container blog-post__container">
            <div class="blog-post__content inner-container">
                <div class="inner-wrapper">
                    <?php // get_template_part('lib/components/share'); ?>
                    <div class="blog-post__details">
                        <div class="blog-post__author-info">
                            <?php
                            // Get author avatar with srcset support using WordPress built-in functionality
                            $author_id = get_the_author_meta('ID');
                            echo get_avatar($author_id, 150, '', get_the_author(), array(
                                'class' => 'blog-post__author-avatar'
                            ));
                            ?>
                            <div class="blog-post__author-text">
                                <span class="blog-post__detail"><span><?php echo get_the_author(); ?></span></span>
                                <span class="blog-post__detail"><span><?php echo get_the_date('F j Y'); ?></span></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="sidebar__spacer"></div>
            </div>
        </div>
    </div>
    <?php
    get_template_part('lib/blocks/page-builder');
    ?>
</section>

