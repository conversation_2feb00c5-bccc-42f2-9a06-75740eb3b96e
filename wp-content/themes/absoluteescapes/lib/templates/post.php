<?php

/**
 * Blog Post
 */

get_template_part('lib/blocks/page-header');

get_template_part('lib/components/breadcrumbs');

?>

<section class="blog-post">
    <div class="blog-post__inner">
        <div class="container blog-post__container">
            <div class="blog-post__content inner-container">
                <div class="inner-wrapper">
                    <?php // get_template_part('lib/components/share'); ?>
                    <div class="blog-post__details">
                        <div class="blog-post__author-info">
                            <?php
                            // Get author avatar with srcset support
                            $author_id = get_the_author_meta('ID');
                            $avatar_html = get_avatar($author_id, 150, '', get_the_author(), array('class' => 'blog-post__author-avatar'));

                            // Check if Simple Local Avatars plugin is active and get high-res versions
                            if (function_exists('get_simple_local_avatar_url')) {
                                $avatar_url_150 = get_simple_local_avatar_url($author_id, 150);
                                $avatar_url_300 = get_simple_local_avatar_url($author_id, 300);

                                if ($avatar_url_150) {
                                    $srcset = $avatar_url_150 . ' 150w';
                                    if ($avatar_url_300) {
                                        $srcset .= ', ' . $avatar_url_300 . ' 300w';
                                    }

                                    $avatar_html = sprintf(
                                        '<img src="%s" srcset="%s" sizes="150px" alt="%s" class="blog-post__author-avatar" width="150" height="150">',
                                        esc_url($avatar_url_150),
                                        esc_attr($srcset),
                                        esc_attr(get_the_author())
                                    );
                                }
                            }

                            echo $avatar_html;
                            ?>
                            <div class="blog-post__author-text">
                                <span class="blog-post__detail"><span><?php echo get_the_author(); ?></span></span>
                                <span class="blog-post__detail"><span><?php echo get_the_date('F j Y'); ?></span></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="sidebar__spacer"></div>
            </div>
        </div>
    </div>
    <?php
    get_template_part('lib/blocks/page-builder');
    ?>
</section>

