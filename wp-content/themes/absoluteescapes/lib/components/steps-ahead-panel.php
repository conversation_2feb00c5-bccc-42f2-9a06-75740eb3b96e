<?php
/**
 * Steps Ahead Panel Component
 * Displays steps ahead content on listing pages after the bottom CTA
 * Content comes from Theme Settings for most pages, All Holidays archive has its own content
 */

$page_obj = get_queried_object();
$panel_settings = null;
$panel_title = '';
$panel_copy = '';
$panel_columns = [];

// Check if we're on a supported page type and get the appropriate settings
if (is_tax('holiday-type')) {
    // Holiday Type taxonomy page - now has its own content
    $panel_settings = get_field('steps_ahead_panel', $page_obj);

    if (!$panel_settings || !$panel_settings['panel_status']) {
        return;
    }

    // Use local content from taxonomy page, fall back to global if empty
    $panel_title = $panel_settings['panel_title'] ?: '';
    $panel_columns = $panel_settings['columns'] ?: [];

    // If no local content, fall back to global content
    if (empty($panel_title) && empty($panel_columns)) {
        $global_content = get_field('steps_ahead_global_content', 'option');
        if ($global_content && $global_content['panel_status']) {
            $panel_title = $global_content['panel_title'] ?: '';
            $panel_columns = $global_content['columns'] ?: [];
        }
    }

} elseif (is_tax('holiday-regions')) {
    // Holiday Regions taxonomy page - now has its own content
    $panel_settings = get_field('steps_ahead_panel', $page_obj);

    if (!$panel_settings || !$panel_settings['panel_status']) {
        return;
    }

    // Use local content from taxonomy page, fall back to global if empty
    $panel_title = $panel_settings['panel_title'] ?: '';
    $panel_columns = $panel_settings['columns'] ?: [];

    // If no local content, fall back to global content
    if (empty($panel_title) && empty($panel_columns)) {
        $global_content = get_field('steps_ahead_global_content', 'option');
        if ($global_content && $global_content['panel_status']) {
            $panel_title = $global_content['panel_title'] ?: '';
            $panel_columns = $global_content['columns'] ?: [];
        }
    }

} elseif (is_singular('holiday')) {
    // Individual Holiday page - check global holiday panel status first
    // Get holiday styling as a top-level field
    $holiday_styling = get_field('holiday_page_styling', 'option');

    // Temporary: If holiday_page_styling doesn't exist yet, assume it's enabled
    if (!$holiday_styling) {
        $holiday_styling = array('panel_status' => true); // Temporary fallback
    }

    if (!$holiday_styling['panel_status']) {
        return; // Holiday panels are globally disabled
    }

    $holiday_id = get_the_ID();
    $panel_title = '';
    $panel_columns = [];
    $content_found = false;

    // Try to get content from holiday types first
    $holiday_types = get_the_terms($holiday_id, 'holiday-type');
    if ($holiday_types && !is_wp_error($holiday_types)) {
        foreach ($holiday_types as $holiday_type) {
            $type_panel = get_field('steps_ahead_panel', $holiday_type);
            if ($type_panel && $type_panel['panel_status']) {
                $type_title = $type_panel['panel_title'] ?: '';
                $type_columns = $type_panel['columns'] ?: [];

                // Check if columns actually have content
                $has_column_content = false;
                if (!empty($type_columns)) {
                    foreach ($type_columns as $column) {
                        // Check if column has image, title, or actual points content
                        $has_points_content = false;
                        if (!empty($column['points'])) {
                            foreach ($column['points'] as $point) {
                                if (!empty($point['title']) || !empty($point['text'])) {
                                    $has_points_content = true;
                                    break;
                                }
                            }
                        }

                        if (!empty($column['image']) || !empty($column['title']) || $has_points_content) {
                            $has_column_content = true;
                            break;
                        }
                    }
                }

                // If this type has actual content, use it
                if (!empty($type_title) || $has_column_content) {
                    $panel_title = $type_title;
                    $panel_columns = $type_columns;
                    $content_found = true;
                    break; // Use first type with content
                }
            }
        }
    }

    // If no content from holiday types, try holiday regions
    if (!$content_found) {
        $holiday_regions = get_the_terms($holiday_id, 'holiday-regions');
        if ($holiday_regions && !is_wp_error($holiday_regions)) {
            foreach ($holiday_regions as $holiday_region) {
                $region_panel = get_field('steps_ahead_panel', $holiday_region);
                if ($region_panel && $region_panel['panel_status']) {
                    $region_title = $region_panel['panel_title'] ?: '';
                    $region_columns = $region_panel['columns'] ?: [];

                    // Check if columns actually have content
                    $has_column_content = false;
                    if (!empty($region_columns)) {
                        foreach ($region_columns as $column) {
                            // Check if column has image, title, or actual points content
                            $has_points_content = false;
                            if (!empty($column['points'])) {
                                foreach ($column['points'] as $point) {
                                    if (!empty($point['title']) || !empty($point['text'])) {
                                        $has_points_content = true;
                                        break;
                                    }
                                }
                            }

                            if (!empty($column['image']) || !empty($column['title']) || $has_points_content) {
                                $has_column_content = true;
                                break;
                            }
                        }
                    }

                    // If this region has actual content, use it
                    if (!empty($region_title) || $has_column_content) {
                        $panel_title = $region_title;
                        $panel_columns = $region_columns;
                        $content_found = true;
                        break; // Use first region with content
                    }
                }
            }
        }
    }

    // If still no content found, fall back to global content
    if (!$content_found) {
        $global_content = get_field('steps_ahead_global_content', 'option');
        if ($global_content && $global_content['panel_status']) {
            $panel_title = $global_content['panel_title'] ?: '';
            $panel_columns = $global_content['columns'] ?: [];
            $content_found = true;
        }
    }

    // If still no content found anywhere, don't display panel
    if (!$content_found) {
        return;
    }

    // Set a dummy panel_settings for the styling logic below
    $panel_settings = array('panel_status' => true);

} elseif (is_post_type_archive('holiday')) {
    // All Holidays archive page - get styling settings from archive page, content from Theme Settings
    $panel_settings = get_field('steps_ahead_panel', 'option');

    // Get global content from Theme Settings
    $global_content = get_field('steps_ahead_global_content', 'option');
    if (!$global_content || !$global_content['panel_status']) {
        return;
    }

    // Use global content for title and columns (same as home page logic)
    $panel_title = $global_content['panel_title'] ?: '';
    $panel_columns = $global_content['columns'] ?: [];

} else {
    // Not a supported page type
    return;
}

// Panel status is now checked in individual page type sections above

// Use appropriate styling fields based on page type
if (is_singular('holiday')) {
    // Individual holiday page - use global holiday page styling from main Steps Ahead options
    // $holiday_styling is already loaded above for status check
    $background_color = isset($holiday_styling['panel_background']) ? $holiday_styling['panel_background'] : '#ffffff';
    $curved_edge = isset($holiday_styling['curve_position']) ? $holiday_styling['curve_position'] : 'none';
    $flip_curved_edge = isset($holiday_styling['curve_flip']) ? $holiday_styling['curve_flip'] : false;
    $point_title_color = isset($holiday_styling['point_title_color']) ? $holiday_styling['point_title_color'] : '#75ada4';
    $column_background_color = isset($holiday_styling['point_background']) ? $holiday_styling['point_background'] : '#ebf2f1';


} else {
    // Type/region listing pages - use standard styling fields from type_page_styling or region_page_styling group
    $page_styling = null;
    if (is_tax('holiday-type')) {
        $page_styling = ($panel_settings && isset($panel_settings['type_page_styling'])) ? $panel_settings['type_page_styling'] : array();
    } elseif (is_tax('holiday-regions')) {
        $page_styling = ($panel_settings && isset($panel_settings['region_page_styling'])) ? $panel_settings['region_page_styling'] : array();
    }

    $background_color = isset($page_styling['panel_background']) ? $page_styling['panel_background'] : '#ebf2f1';
    $curved_edge = isset($page_styling['curve_position']) ? $page_styling['curve_position'] : 'bottom';
    $flip_curved_edge = isset($page_styling['curve_flip']) ? $page_styling['curve_flip'] : false;
    $point_title_color = isset($page_styling['point_title']) ? $page_styling['point_title'] : '#75ada4';
    $column_background_color = isset($page_styling['point_background']) ? $page_styling['point_background'] : '#ffffff';
}

// Set up background classes
$background_classes = '';
$has_background = $background_color && $background_color !== 'transparent' && $background_color !== '';
if ($has_background) {
    $background_classes .= ' has-background';
}
if ($curved_edge && $curved_edge !== 'none') {
    $background_classes .= ' curve-' . $curved_edge;
    if ($flip_curved_edge) {
        $background_classes .= ' curve-flipped';
    }
}

?>

<section class="steps-ahead steps-ahead--listing">
    <div class="steps-ahead__inner<?php echo $background_classes ? ' ' . $background_classes : ''; ?>" data-aos="fade"<?php if ($has_background) : ?> style="background-color: <?php echo $background_color; ?>;"<?php endif; ?>>
        <div class="container steps-ahead__container">
            <div class="steps-ahead__content centre inner-container">
                <?php if($panel_title) : ?>
                    <h2 class="steps-ahead__heading h2-large heading-light heading-underlined text-weight-regular"><?php echo esc_html($panel_title); ?></h2>
                <?php endif; ?>
                <?php if($panel_copy) : ?>
                    <div class="steps-ahead__copy content-area copy-large">
                        <?php echo $panel_copy; ?>
                    </div>
                <?php endif; ?>
            </div>

            <?php if($panel_columns) : ?>
                <div class="row steps-ahead__row">
                    <?php foreach($panel_columns as $column) : ?>
                        <?php
                        // Handle field structure from Theme Settings:
                        // Theme Settings: title, image, points (with title, text)
                        $ctitle = $column['title'] ?: '';
                        $cimage = $column['image'] ?: null;
                        $cpoints = $column['points'] ?: [];
                        ?>

                        <div class="col-md-3 steps-ahead__col">
                            <div class="steps-ahead__col-content centre"<?php if($column_background_color) : ?> style="background-color: <?php echo $column_background_color; ?>;"<?php endif; ?>>
                                <?php if($cimage) : ?>
                                    <div class="steps-ahead__col-image">
                                        <?php
                                        // Use WordPress built-in responsive image with srcset
                                        $image_id = $cimage['ID'];
                                        $image_url = wp_get_attachment_image_url($image_id, 'full');
                                        $image_metadata = wp_get_attachment_metadata($image_id);

                                        // Build srcset from metadata
                                        $srcset_array = array();
                                        if ($image_metadata && isset($image_metadata['sizes'])) {
                                            $base_url = dirname($image_url) . '/';

                                            // Add original image
                                            $srcset_array[] = $image_url . ' ' . $image_metadata['width'] . 'w';

                                            // Add thumbnail sizes
                                            foreach ($image_metadata['sizes'] as $size_name => $size_data) {
                                                if (isset($size_data['width']) && isset($size_data['file'])) {
                                                    $size_url = $base_url . $size_data['file'];
                                                    $srcset_array[] = $size_url . ' ' . $size_data['width'] . 'w';
                                                }
                                            }
                                        }
                                        $srcset = implode(', ', $srcset_array);
                                        ?>
                                        <img src="<?php echo esc_url($image_url); ?>"
                                             srcset="<?php echo esc_attr($srcset ?: $image_url); ?>"
                                             sizes="(max-width: 480px) 100vw, (max-width: 991px) 50vw, 25vw"
                                             alt="<?php echo esc_attr($cimage['alt'] ?: ''); ?>"
                                             style="width: 100%; height: 100%; object-fit: cover;">
                                        <?php if($ctitle) : ?>
                                            <h4 class="steps-ahead__col-heading"><?php echo esc_html($ctitle); ?></h4>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>

                                <?php if($cpoints && !empty($cpoints)) : ?>
                                    <div class="steps-ahead__col-points">
                                        <?php foreach($cpoints as $point) : ?>
                                            <?php
                                            $point_title = $point['title'] ?: '';
                                            $point_text = $point['text'] ?: '';
                                            ?>
                                            <?php if($point_title || $point_text) : ?>
                                                <div class="steps-ahead__point">
                                                    <?php if($point_title) : ?>
                                                        <h5 class="steps-ahead__point-title" style="color: <?php echo $point_title_color; ?>;"><?php echo esc_html($point_title); ?></h5>
                                                    <?php endif; ?>
                                                    <?php if($point_text) : ?>
                                                        <div class="steps-ahead__point-text">
                                                            <?php echo nl2br(esc_html($point_text)); ?>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section><!-- .steps-ahead -->
