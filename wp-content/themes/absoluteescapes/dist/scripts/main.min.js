const DEBUG_MODE=!1,debug={log:(...e)=>DEBUG_MODE&&console.log(...e),warn:(...e)=>DEBUG_MODE&&console.warn(...e)};function trackEvents(e,t,o,a={}){if(debug.log("🎯 Initializing tracking:",{triggerEvent:e,selector:t,eventType:o,eventData:a}),!e&&!t)return debug.log("📡 Direct event firing mode"),void("function"==typeof gtag?(debug.log("📊 Sending direct event to GA:",{eventType:o,eventData:a}),gtag("event",o,a)):debug.warn("⚠️ gtag not found - event not sent"));const n=Array.isArray(t)?t:[t];debug.log("🎯 Tracking selectors:",n);const r=document.querySelectorAll(n.join(","));debug.log(`🔍 Found ${r.length} elements to track`),r.forEach(n=>{n.addEventListener(e,function(e){debug.log("🎯 Tracked element clicked:",n);var t={text:n.textContent.replace(/[\n\r\s]+/g," ").trim(),classes:n.classList.value,url:n.href||n.querySelector('a[href^="tel:"]')&&n.querySelector('a[href^="tel:"]').href||"",id:n.id};debug.log("📊 Element data:",t),"function"==typeof gtag?(t={...t,button_name:t.text,screen_name:window.location.pathname,...a},debug.log("📡 Sending event to GA:",{eventType:o,...t}),gtag("event",o,t)):debug.warn("⚠️ gtag not found - event not sent")})})}debug.log("🚀 Analytics tracking script loaded"),document.addEventListener("DOMContentLoaded",function(){debug.log("📱 Setting up phone link tracking"),trackEvents("click",[".masthead__details-item.masthead__phone",".mastfoot__contact-item.mastfoot__phone",'.listing-cta.cta-top a[href^="tel:"]','.listing-cta.cta-bottom a[href^="tel:"]'],"button_click")}),jQuery(document).on("gform_confirmation_loaded",function(e,t){if(debug.log("📝 Gravity Form confirmation loaded:",t),1===t||7===t){debug.log("📝 Processing form submission:",t);const o=jQuery(`#gform_confirmation_wrapper_${t}`);var n={form_id:t,form_message:o.text().replace(/[\n\r\s]+/g," ").trim(),screen_name:window.location.pathname};debug.log("📝 Form submitted with parameters:",n),"function"==typeof gtag&&gtag("event","form_submit",n)}else debug.log("ℹ️ Ignoring non-tracked form:",t)});
var geoipCountryCode=null,geoipPromise=null;function populateGeoIPForVisibleForms(){if(console.log("🌍 GeoIP detection starting for visible forms..."),"undefined"!=typeof geoip_detect)try{if(geoipCountryCode)return console.log("🌍 Using cached country code:",geoipCountryCode),void setGeoIPForVisibleForms(geoipCountryCode);if(geoipPromise)return console.log("🌍 Using existing GeoIP promise"),void geoipPromise.then(function(o){o&&setGeoIPForVisibleForms(o)});console.log("🌍 Creating new GeoIP detection promise"),(geoipPromise=geoip_detect.get_info().then(function(o){return console.log("🌍 GeoIP record received:",o),o.error()?(console.warn("🌍 GeoIP detection failed:",o.error()),null):(geoipCountryCode=o.get_with_locales("country.isoCode",["en"]),console.log("🌍 GeoIP country code cached:",geoipCountryCode),geoipCountryCode)}).catch(o=>(console.warn("🌍 GeoIP detection error (non-critical):",o),null))).then(function(o){o&&setGeoIPForVisibleForms(o)}),setTimeout(()=>{geoipCountryCode||console.warn("🌍 GeoIP detection timeout - continuing without geoip")},3e3)}catch(o){console.warn("🌍 Critical error in geoip detection (non-critical):",o)}else console.warn("🌍 geoip_detect is not available - forms will work without geoip")}function setGeoIPForVisibleForms(n){const o=document.querySelectorAll('form[id^="gform_"]:not([style*="display: none"])');console.log("🌍 Found visible forms:",o.length),o.forEach(o=>{const e=o.querySelector(".geoip_country");if(e){const i=e.querySelector("input");i&&!i.value&&(i.value=n,console.log("🌍 Set geoip value for form:",o.id,"to:",n))}})}
$(function(){var n=$("input[value='holiday']"),i=$("input[value='holiday_title']"),a=$("input[value='holiday_type']"),s=$("input[value='tourcode']"),c=$("input[value='itinerary']"),l=$("input[value='accommodation']"),r=$("input[value='startdate']");jQuery(document).on("gform_post_render",function(o,t){console.log("📝 Form post render event fired for form ID:",t),"function"==typeof populateGeoIPForVisibleForms&&(console.log("🔧 Form rendered - triggering GeoIP population"),setTimeout(function(){populateGeoIPForVisibleForms()},100));var e=$('form[id^="gform_"]');console.log("📝 Total forms found on page:",e.length),e.each(function(o,t){console.log("📝 Form found:",t.id,"visible:",$(t).is(":visible"))}),1!==t&&($(".gfmc-column").append('<div class="form__next"><span class="button button--alt">Next <i class="fas fa-chevron-right"></i></span><div>'),$(".form").find(".gform_footer").appendTo(".gfmc-column:last"),$(".gform_confirmation_wrapper").length&&($(".form__section").remove(),$(".form__heading-wrapper").remove()),$(".gsection").first().addClass("active"),$(".gsection").first().next().addClass("active"),$(".validation_message").length&&($(".validation_message").closest(".gfmc-column").addClass("active"),$(".validation_message").closest(".gfmc-column").prev().addClass("active"))),2===t&&(n.val($(".form__section").attr("data-id")),i.val($(".form__section").attr("data-title")),a.val($(".form__section").attr("data-type")),s.val($(".tour-code").text()),c.val($("#formItinerary").val()),l.val($("#formAccommodation").val()),r.val($("#formStart").val()),$("#formItinerary").on("change",function(){c.val($(this).val())}),$("#formAccommodation").on("change",function(){l.val($(this).val())}),$("#formStart").on("change",function(){r.val($(this).val())})),$(".validation_error").length&&$(".payment-choice").find("input").attr("checked",!1),$(document).find(".payment-choice").find("input").on("change",function(){console.log("💳 Payment choice changed:",$(this).val(),"checked:",$(this).is(":checked")),"bank transfer"!==$(this).val().toLowerCase()?($(".form--payment").find(".gsection").show(),$(".payment-details__notice").removeClass("active"),$(".gfmc-row-2-column").addClass("active"),$(".gfmc-row-2-column").prev().addClass("active")):($(".form--payment").find(".gsection").hide(),$(".form--payment").find(".gfmc-column").removeClass("active"),$(".form--payment").find(".gsection").removeClass("active"),$(".payment-details__notice").addClass("active"),$(".gsection").first().addClass("active"))})}),gform.addFilter("gform_datepicker_options_pre_init",function(o,t,e){return o.yearRange="-0:+5",o}),$(document).on("gform_confirmation_loaded",function(o,t){2!==t&&3!==t||$(".gform_confirmation_wrapper").length&&($(".form__section").remove(),$(".form__heading-wrapper").remove())}),$(document).on("click",".form__next .button",function(){var o=$(this);$(this).closest(".gfmc-column").removeClass("active"),$(this).closest(".gfmc-column").next().is(":visible")?($(this).closest(".gfmc-column").next().addClass("active"),$(this).closest(".gfmc-column").next().next().addClass("active")):($(this).closest(".gfmc-column").next().next().next().addClass("active"),$(this).closest(".gfmc-column").next().next().next().next().addClass("active")),$("html, body").animate({scrollTop:o.closest(".gfmc-column").next().offset().top},0)}),$(document).on("click",".gsection",function(){$(this).next().hasClass("active")?$(this).next().removeClass("active"):($(this).next().addClass("active"),$(this).addClass("active"))}),$("#filterTrigger").on("click",function(o){o.preventDefault(),$(".filter").slideToggle()}),$(".enquiry-form").on("click",function(o){o.target===this&&$(this).removeClass("active")}),$(".enquiry-cta__button").on("click",function(o){if(console.log("🔘 Enquiry button clicked"),o.preventDefault(),$(".enquiry-form").toggleClass("active"),console.log("🔘 Modal toggled, active class:",$(".enquiry-form").hasClass("active")),$(".enquiry-form").hasClass("active")){console.log("🔧 Resetting all Gravity Forms submission flags");for(var t=1;t<=10;t++)!0===window["gf_submitting_"+t]&&(console.log("🔧 Resetting gf_submitting_"+t),window["gf_submitting_"+t]=!1,$("#gform_ajax_spinner_"+t).remove())}}),$(".enquiry-form__close").on("click",function(){console.log("❌ Modal close button clicked"),$(".enquiry-form").removeClass("active")}),$(document).on("change",'input[type="checkbox"]',function(){console.log("☑️ Checkbox changed:",this.name,"checked:",this.checked)}),$(document).on("submit","form",function(o){var t;console.log("📤 Form submission attempted:",this.id),console.log("📤 Form action:",this.action),console.log("📤 Form method:",this.method),console.log("📤 Form has gform_ajax input:",0<$(this).find('input[name="gform_ajax"]').length),this.id.startsWith("gform_")&&(t=this.id.replace("gform_",""),console.log("📤 Gravity Form ID:",t),console.log("📤 gf_submitting_"+t+":",window["gf_submitting_"+t]),!0===window["gf_submitting_"+t]&&(console.log("🔧 Form was stuck in submitting state - resetting"),window["gf_submitting_"+t]=!1,$("#gform_ajax_spinner_"+t).remove(),console.log("🔧 Allowing form submission to proceed")))}),$(document).on("gform_pre_submission",function(o,t,e){console.log("📤 Gravity Form pre-submission:",t.id,"ajax:",e)}),$(document).on("gform_post_submission",function(o,t,e){console.log("📤 Gravity Form post-submission:",t.id,"ajax:",e)}),$(document).on("gform_confirmation_loaded",function(o,t){console.log("✅ Gravity Form confirmation loaded:",t)})});
$(window).on("load",function(){var t=$(".subnav");t.length&&Stickyfill.add(t);var i=$(".enquiry-form");i.length&&Stickyfill.add(i);var n=function(t){for(var i,n=window.location.search.substring(1).split("&"),e=0;e<n.length;e++)if((i=n[e].split("="))[0]===t)return void 0===i[1]||decodeURIComponent(i[1])};function e(t){var i=t.getBoundingClientRect(),n=window.innerHeight||document.documentElement.clientHeight,t=window.innerWidth||document.documentElement.clientWidth,n=i.top<=n&&0<=i.top+(i.height-116),i=i.left<=t&&0<=i.left+i.width;return n&&i}$(document).on("click",".masthead a",function(t){!/#/.test(this.href)||$(this).hasClass("aito-link")||$(this).hasClass("modal-close")||t.preventDefault()}),$(".aito-link").on("click",function(){$(".review-bar").addClass("active")}),$(".modal-close").on("click",function(){$(".review-bar").removeClass("active")}),$(".scroll-next").on("click",function(){$("html, body").animate({scrollTop:$(this).closest("section").next().offset().top-50},500)}),$(".scroll-to").on("click",function(){var t=$(this).data("target");!t||(t=$("#"+t)).length&&$("html, body").animate({scrollTop:t.offset().top-50},500)}),$(".back-to-top").on("click",function(){$("html, body").animate({scrollTop:$("html").offset().top},1e3)});t=$(".reviews__row");function a(){var t=$(".favourite-holidays__col-content"),i=$(".favourite-holidays__background"),n=0;i.css("height",""),t.css("min-height",""),t.each(function(){var t=$(this).outerHeight();n<t&&(n=t)}),t.css("min-height",n+"px");t=Math.max(n+40,445);i.css("height",t+"px")}t.on("ready.flickity",function(){$(this).parent().find(".reviews__button--prev").appendTo($(this).find(".flickity-button.previous")),$(this).parent().find(".reviews__button--next").appendTo($(this).find(".flickity-button.next"))}),t.flickity({wrapAround:!0,contain:!0,freeScroll:!0});i=$(".favourite-holidays__row");i.on("ready.flickity",function(){$(this).parent().find(".favourite-holidays__button--prev").appendTo($(this).find(".flickity-button.previous")),$(this).parent().find(".favourite-holidays__button--next").appendTo($(this).find(".flickity-button.next")),setTimeout(function(){a()},100)}),i.on("change.flickity",function(){setTimeout(function(){a()},50)}),i.flickity({freeScroll:!0,groupCells:!0});var o,s,l,t=(o=function(){$(".favourite-holidays__row").length&&a()},s=250,function(){var t=this,i=arguments;clearTimeout(l),l=setTimeout(function(){l=null,o.apply(t,i)},s)});$(window).on("resize",t);i=$(".destinations__row");i.on("ready.flickity",function(){$(this).parent().find(".favourite-holidays__button--prev").appendTo($(this).find(".flickity-button.previous")),$(this).parent().find(".favourite-holidays__button--next").appendTo($(this).find(".flickity-button.next"))}),i.flickity({wrapAround:!0,pageDots:!1,freeScroll:!0,contain:!0,groupCells:!0,cellAlign:"left"});t=$(".instagram__photos");t.on("ready.flickity",function(){$(this).parent().find(".instagram__button--prev").appendTo($(this).find(".flickity-button.previous")),$(this).parent().find(".instagram__button--next").appendTo($(this).find(".flickity-button.next"))}),t.flickity({pageDots:!1,freeScroll:!0,contain:!0,groupCells:!0,cellAlign:"left"});i=$(".carousel__images");i.on("ready.flickity",function(){$(this).parent().find(".carousel__button--prev").appendTo($(this).find(".flickity-button.previous")),$(this).parent().find(".carousel__button--next").appendTo($(this).find(".flickity-button.next"))}),i.flickity({wrapAround:!0,contain:!0,groupCells:!0}),$(".holidays__gallery").flickity({wrapAround:!0,contain:!0,groupCells:!0,arrowShape:{x0:10,x1:60,y1:50,x2:70,y2:35,x3:35}});t=$(".accommodation__images");t.on("ready.flickity",function(){$(this).parent().find(".accommodation__button--prev").appendTo($(this).find(".flickity-button.previous")),$(this).parent().find(".accommodation__button--next").appendTo($(this).find(".flickity-button.next"))}),t.flickity({wrapAround:!0,contain:!0,groupCells:!0});var r=$(".inf-posts");$(".next-posts-link").length&&r.length&&(r.infiniteScroll({path:".next-posts-link a",append:".inf-posts .inf-post",history:!0,button:".button-inf",scrollThreshold:!1,status:".page-load-status"}),r.on("append.infiniteScroll",function(t,i,n,e){$(e).addClass("appended-item"),r.imagesLoaded(function(){$(e).find("img").each(function(t,i){i.outerHTML=i.outerHTML}),$(document).find(".holidays__gallery").length&&$(document).find(".holidays__gallery").flickity({wrapAround:!0,contain:!0,groupCells:!0,arrowShape:{x0:10,x1:60,y1:50,x2:70,y2:35,x3:35}}),_()})}));i=document.getElementById("distanceRangeSlider");$("#distanceRangeSlider").length&&(noUiSlider.create(i,{start:[1,32],connect:!0,step:1,range:{min:1,max:32}}),t=n("durationmin"),n=n("durationmax"),t&&n?i.noUiSlider.set([t,n]):t?i.noUiSlider.set([t,null]):n&&i.noUiSlider.set([null,n]),i.noUiSlider.on("update",function(t,i){$(".filter__range-number--min").text(Math.floor(t[0])),$("#durationMin").val(Math.floor(t[0])),$(".filter__range-number--max").text(Math.floor(t[1])),$("#durationMax").val(Math.floor(t[1]))}),$("#orderDropdown").on("change",function(){$("#sort").val($(this).val()),$("#filterForm").submit()}),i.noUiSlider.on("change",function(){$("#filterForm").submit()})),$(".filter__input").on("change",function(){$("#filterForm").submit()});var c=!1;$(".filter__label-wrapper").on("click",function(){var t=$(this);c||(c=!0,t.hasClass("collapsed")?t.next().slideDown(function(){t.removeClass("collapsed"),c=!1}):t.next().slideUp(function(){t.addClass("collapsed"),c=!1}))});var d,f=[];$(".page-header__gallery").find("img").each(function(){(d={}).src=$(this).attr("src");var t=$(this).attr("src");t.startsWith("data:image/png;base64")&&(t=$(this).data("src")),d.src=t;t=$(this).attr("alt");t&&(d.caption=t),f.push(d)}),$("#galleryTrigger").on("click",function(t){t.preventDefault(),$.fancybox.open(f,{loop:!0}),$('[data-fancybox="gallery"]').fancybox({afterLoad:function(t,i){i.$image.attr("alt",i.opts.$orig.find("img").attr("alt"))}})});var u=!1;$(".accordion__heading-wrapper").on("click",function(t){var i=$(this);$(t.target).hasClass("button")||0<$(t.target).closest(".button").length||$(t.target).hasClass("itineraries-prices__itinerary-extra")||0<$(t.target).closest(".itineraries-prices__itinerary-extra").length||u||(u=!0,i.parent().hasClass("active")?(i.parent().removeClass("active"),i.next().slideUp(function(){u=!1})):(i.parent().addClass("active"),i.next().slideDown(function(){u=!1})))}),$(".accordion__close").on("click",function(){var t;u||(u=!0,(t=$(this)).parent().parent().slideUp(function(){u=!1,t.parent().parent().parent().removeClass("active")}))}),$("#subNav").on("change",function(){var t=$(this).val();$("html, body").animate({scrollTop:$("#"+t).offset().top-100},0)}),$(".subnav").length&&(setTimeout(function(){$(this).scrollTop()<=150?($(".subnav__link").parent().removeClass("active"),$("#subNav").val("")):$("section").each(function(){if(""!==$(this).prop("id")){var t=$(this);if(e(this))return $(".subnav__link").each(function(){$(this).attr("data-id")===t.prop("id")?($(".subnav__link[data-id="+t.prop("id")+"]").parent().addClass("active"),$("#subNav").val(t.prop("id"))):($(".subnav__link").not(".subnav__link[data-id="+t.prop("id")+"]").parent().removeClass("active"),$("#subNav").val(""))}),!1;$(".subnav__link").not(".subnav__link[data-id="+t.prop("id")+"]").parent().removeClass("active"),$("#subNav").val("")}})},50),$(window).on("scroll",function(){$(this).scrollTop()<=150?($(".subnav__link").parent().removeClass("active"),$("#subNav").val("")):$("section").each(function(){if(""!==$(this).prop("id")){var t=$(this);if(e(this))return $(".subnav__link").each(function(){$(this).attr("data-id")===t.prop("id")?($(".subnav__link[data-id="+t.prop("id")+"]").parent().addClass("active"),$("#subNav").val(t.prop("id"))):$(".subnav__link").not(".subnav__link[data-id="+t.prop("id")+"]").parent().removeClass("active")}),!1;$(".subnav__link").not(".subnav__link[data-id="+t.prop("id")+"]").parent().removeClass("active"),$("#subNav").val("")}})})),$('[data-toggle="datepicker"]').datepicker({format:"dd/mm/yyyy",autoHide:!0}),$(".tooltip").on("click",function(){$(this).toggleClass("active")}),$(".search-trigger").on("click",function(){$(".masthead__form-container").toggleClass("active"),$(".masthead__form-container").hasClass("active")&&$(".masthead__form > input").focus()}),$(".masthead__form-close").on("click",function(){$(".masthead__form-container").removeClass("active")});var p=!1;if($(".overview__bottom-copy-trigger").on("click",function(){p||(p=!0,$(this).hasClass("active")?($(this).removeClass("active"),$(this).parent().next().slideUp(function(){p=!1})):($(this).addClass("active"),$(this).parent().next().slideDown(function(){p=!1})))}),$(".banner__slideshow").length){var h=$(".banner__slideshow"),i=h.find(".banner__slide").length;if(console.log("Banner slideshow found. Slide count:",i),1<i)try{h.addClass("fade-enabled"),h.flickity({autoPlay:6e3,wrapAround:!0,pauseAutoPlayOnHover:!1,prevNextButtons:!0,pageDots:!1,draggable:!1,accessibility:!1,setGallerySize:!1}),console.log("Flickity initialized with autoPlay: 6000"),h.on("ready.flickity",function(){var t=h.find(".flickity-button.previous"),i=h.find(".flickity-button.next");t.empty().html('<svg class="svg-inline--fa banner-prev-arrow" aria-hidden="true" focusable="false" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" preserveAspectRatio="xMidYMid meet"><path fill="currentColor" d="M285.476 272.971L91.132 467.314c-9.373 9.373-24.569 9.373-33.941 0l-22.667-22.667c-9.357-9.357-9.375-24.522-.04-33.901L188.505 256 34.484 101.255c-9.335-9.379-9.317-24.544.04-33.901l22.667-22.667c9.373-9.373 24.569-9.373 33.941 0L285.475 239.03c9.373 9.372 9.373 24.568.001 33.941z"></path></svg>'),i.empty().html('<svg class="svg-inline--fa banner-next-arrow" aria-hidden="true" focusable="false" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" preserveAspectRatio="xMidYMid meet"><path fill="currentColor" d="M285.476 272.971L91.132 467.314c-9.373 9.373-24.569 9.373-33.941 0l-22.667-22.667c-9.357-9.357-9.375-24.522-.04-33.901L188.505 256 34.484 101.255c-9.335-9.379-9.317-24.544.04-33.901l22.667-22.667c9.373-9.373 24.569-9.373 33.941 0L285.475 239.03c9.373 9.372 9.373 24.568.001 33.941z"></path></svg>');i=h.find(".banner__slide");i.each(function(t){var i=$(this);i.css({position:"absolute",top:"0",left:"0",width:"100%",height:"100%",transform:"none"}),0<t&&i.css("opacity","0")});i=i.filter(".is-selected");i.css("opacity","1"),h.find(".flickity-slider").css("transform","none");i=i.find(".banner__heading");i.length&&i.css({opacity:"0"}).animate({opacity:"1"},800)}),h.on("change.flickity",function(){var t=h.find(".banner__slide"),i=t.filter(".is-selected");h.find(".flickity-slider").css("transform","none"),t.css({position:"absolute",top:"0",left:"0",width:"100%",height:"100%",opacity:"0",transform:"none"}),i.css("opacity","1");var n=i.find(".banner__heading");n.length&&(n.css({opacity:"0"}),setTimeout(function(){n.animate({opacity:"1"},600)},200))})}catch(t){console.error("Error initializing Flickity:",t)}else{var v=h.find(".banner__slide").find(".banner__heading");v.length&&(v.css({opacity:"0"}),setTimeout(function(){v.animate({opacity:"1"},800)},500))}}function _(){$(".holidays__post-row").off("click.holidayRow").on("click.holidayRow",function(t){$(t.target).closest(".holidays__gallery, a, .flickity-button").length||(t=$(this).find(".holidays__title").closest("a")).length&&t.attr("href")&&(window.location.href=t.attr("href"))})}_(),AOS.init({once:!0}),$(".overlay").fadeOut()});
$(function(){var a=$(".masthead"),s=$(".masthead__burger"),e=$(".navigation"),i=$(".review-bar--mobile"),n=$(".subnav");s.on("click",function(){$(this).toggleClass("active"),$(this).hasClass("active")?(a.addClass("active-nav"),e.addClass("active"),$("html, body").addClass("overflow-menu")):(a.removeClass("active"),e.removeClass("active"),$("html, body").removeClass("overflow-menu"))}),$(".navigation__close").on("click",function(){a.removeClass("active"),e.removeClass("active"),e.removeClass("submenu-active"),$("html, body").removeClass("overflow-menu"),s.removeClass("active"),$(".sub-menu").removeClass("active")}),$(".navigation").find(".menu-item-has-children").append('<span class="sub-arrow"></span>'),$(".navigation").find(".sub-menu").each(function(){var s=$(this).prev().text();$(this).prepend('<li><a class="back-link" href="#">< '+s+"</a></li>")}),$(document).on("click",".sub-arrow",function(){$(this).prev().addClass("active"),e.addClass("submenu-active"),e.scrollTop(0)}),$(document).on("click",".back-link",function(){$(this).parent().parent().removeClass("active"),$(this).parent().parent().parent().parent().hasClass("menu")&&e.removeClass("submenu-active"),e.scrollTop(0)}),$(".navigation").find("a[href*='#']").on("click",function(){$(this).hasClass("back-link")||($(this).next().addClass("active"),e.addClass("submenu-active"),e.scrollTop(0))});var l=!1;$(".mastfoot").find(".menu-item-has-children > a").on("click",function(s){s.preventDefault(),l||(l=!0,$(this).hasClass("active")?($(this).removeClass("active"),$(this).next(".sub-menu").slideUp(function(){l=!1})):($(this).addClass("active"),$(this).next(".sub-menu").slideDown(function(){l=!1})))});var t=0;$(window).on("scroll",function(){var s,e=$(this).scrollTop();a.hasClass("masthead--static")||(150<e?(a.addClass("is-fixed"),t<e?a.hasClass("is-visible")&&(a.addClass("is-hidden"),a.removeClass("is-visible")):(a.removeClass("is-hidden"),a.addClass("is-visible"))):e<1&&(a.removeClass("is-fixed"),a.removeClass("is-hidden"),a.removeClass("is-visible"))),i.length&&(s=$(window).width()<=480?115:125,s=n.length&&s<=e,100<e?t<e?i.hasClass("is-hidden")||(i.addClass("is-hidden"),i.removeClass("is-visible"),n.length&&n.addClass("review-bar-hidden")):s?(i.addClass("is-hidden"),i.removeClass("is-visible"),n.length&&n.addClass("review-bar-hidden")):(i.removeClass("is-hidden"),i.addClass("is-visible"),n.length&&n.removeClass("review-bar-hidden")):e<50&&(i.removeClass("is-hidden"),i.removeClass("is-visible"),n.length&&n.removeClass("review-bar-hidden"))),t=e})});
$(function(){$(".filter-links").move({breakpoint:991,oldLocation:".holidays-results",newLocation:".filter",methods:{o:"insertBefore",n:"insertAfter"}}),$(".holidays-results__sort-by-wrapper").move({breakpoint:991,oldLocation:".holidays-results__order",newLocation:".holidays-results__sort-by-trigger-wrapper",methods:{o:"appendTo",n:"appendTo"}}),$(".masthead__form-header").move({breakpoint:1160,oldLocation:".masthead__details",newLocation:".form__container",methods:{o:"insertBefore",n:"prependTo"}})});