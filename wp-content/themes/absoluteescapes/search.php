<?php 

/**
 * Search
 */

get_header();

?>

<div class="search-results">
    <div class="search-results__inner">
        <div class="container search-results__container">
            <div class="search-results__content">
                <?php if(have_posts()) : ?>
                <h1 class="search-results__title centre"><?php _e("Search results for", 'absoluteescapes'); ?> '<?php echo $_GET['s']; ?>'</h1>
                    <div class="search-results__posts">
                        <?php while(have_posts()) : the_post(); ?>
                            <?php 
                            
                            $title = get_the_title();
                            
                            ?>
                            <div class="search-results__post">
                                <a href="<?php echo get_the_permalink(get_the_ID()); ?>" class="search-results__post-link">
                                    <div class="search-results__post-inner">
                                        <?php if($title) : ?>
                                            <h3 class="search-results__title"><?php echo $title; ?></h3>
                                        <?php endif; ?>
                                        <?php if(has_excerpt(get_the_ID())) : ?>
                                            <div class="search-results__copy">
                                                <p><?php echo get_the_excerpt(); ?> <i class="fas fa-chevron-right"></i></p>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </a>
                            </div>
                        <?php endwhile; ?>
                    </div>
                    <?php if (paginate_links()) : ?>
                        <div class="paginate-links sans-text centre">
                            <?php echo paginate_links(['prev_next' => false, 'end_size' => 1, 'mid_size' => 2]); ?>
                        </div>
                    <?php endif; ?>
                <?php else : ?>
                    <div class="no-results centre">
                        <h1><?php _e('No results found', 'absoluteescapes'); ?></h1>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php 

get_footer();

?>